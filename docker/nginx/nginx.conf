server {
    listen 80;
    server_name localhost;
    root /var/www/html/public;
    server_tokens off;

    autoindex off;
    etag off;

    if ($server_protocol ~* "HTTP/1.0") {
        return 444;
    }
    ssi off;
    add_header X-XSS-Protection "1; mode=block" always;

    add_header X-Frame-Options "SAMEORIGIN" always;

    proxy_cookie_path / "/; HttpOnly; Secure";
    add_header Set-Cookie "Path=/; HttpOnly; Secure";

    if ($request_method = TRACE) {
        return 405;
    }

    if ($request_method !~ ^(GET|HEAD|POST|PUT|DELETE)$) {
        return 405;
    }

    index index.php;

    charset utf-8;

    client_max_body_size 150M;

    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    gzip_min_length 1000;
    gzip_comp_level 6;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
        autoindex off;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        expires 30d;
        add_header Cache-Control "public, no-transform";
        etag off;
        if_modified_since off;
        add_header Last-Modified "";
    }

    error_page 404 /index.php;

    location ~ \.php$ {
        fastcgi_pass app:9000;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;

        fastcgi_read_timeout 600;
        fastcgi_send_timeout 600;
        fastcgi_connect_timeout 60;

        fastcgi_buffers 16 16k;
        fastcgi_buffer_size 32k;

        # Hide PHP version and presence
        fastcgi_hide_header X-Powered-By;
        fastcgi_param PHP_VALUE "upload_max_filesize=150M \n post_max_size=150M \n expose_php=Off";
    }

    location ~ /\.(?!well-known).* {
        limit_except GET POST { deny all; }
        deny all;
    }

    location ~ \.(htaccess|htpasswd|conf|ini|txt|log)$ {
        deny all;
    }
}
