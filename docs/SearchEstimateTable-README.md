# SearchEstimateTable - Componente de Tabla Paginable

## Descripción

`SearchEstimateTable` es un componente Livewire reutilizable que crea una tabla paginable con estilo Filament. Está diseñado para mostrar cotizaciones con funcionalidades de búsqueda y paginación.

## Características

- ✅ **Paginación**: Navegación por páginas con controles intuitivos
- ✅ **Búsqueda en tiempo real**: Filtrado por código, cliente, RNC/cédula, plan y referidor
- ✅ **Estilo Filament**: Diseño consistente con el framework Filament
- ✅ **Responsive**: Adaptable a diferentes tamaños de pantalla
- ✅ **Modo oscuro**: Soporte completo para tema oscuro
- ✅ **Acciones personalizables**: Botones de emitir, editar y descargar
- ✅ **Estado vacío**: Mensaje informativo cuando no hay datos

## Instalación

El componente ya está creado en tu proyecto Laravel. Los archivos son:

- **Componente PHP**: `app/Livewire/SearchEstimateTable.php`
- **Vista Blade**: `resources/views/livewire/search-estimate-table.blade.php`

## Uso Básico

### 1. En una página Filament

```php
// En tu página Filament
<x-filament-panels::page>
    @livewire('search-estimate-table', ['cotizaciones' => $miArrayDeCotizaciones])
</x-filament-panels::page>
```

### 2. En cualquier vista Blade

```php
@livewire('search-estimate-table', ['cotizaciones' => $cotizaciones])
```

### 3. Desde un controlador

```php
public function index()
{
    $cotizaciones = [
        [
            'id' => 1,
            'fecha_cotizacion' => '20/06/2025',
            'codigo' => 'COT-001',
            'cliente' => 'Juan Pérez García',
            'rnc_cedula' => '001-1234567-8',
            'plan' => 'Plan Premium',
            'referidor' => 'María González',
            'emitir_url' => '/cotizaciones/emitir/1',
            'editar_url' => '/cotizaciones/editar/1',
            'descargar_url' => '/cotizaciones/descargar/1'
        ],
        // ... más cotizaciones
    ];

    return view('mi-vista', compact('cotizaciones'));
}
```

## Formato del Array

El componente espera un array de cotizaciones con la siguiente estructura:

```php
[
    [
        'id' => 1,                                    // ID único (opcional)
        'fecha_cotizacion' => '20/06/2025',          // Fecha en formato dd/mm/yyyy
        'codigo' => 'COT-001',                       // Código de cotización
        'cliente' => 'Juan Pérez García',            // Nombre completo del cliente
        'rnc_cedula' => '001-1234567-8',            // RNC o cédula
        'plan' => 'Plan Premium',                    // Nombre del plan
        'referidor' => 'María González',             // Nombre del referidor
        'emitir_url' => '/cotizaciones/emitir/1',    // URL para emitir (opcional)
        'editar_url' => '/cotizaciones/editar/1',    // URL para editar (opcional)
        'descargar_url' => '/cotizaciones/descargar/1' // URL para descargar (opcional)
    ]
]
```

### Campos Obligatorios

Todos los campos son opcionales, pero se recomienda incluir al menos:
- `codigo`: Para identificar la cotización
- `cliente`: Para mostrar el nombre del cliente
- `fecha_cotizacion`: Para mostrar cuándo se creó

### Campos Opcionales

- `id`: Si se incluye, se mostrarán los botones de acción
- `emitir_url`, `editar_url`, `descargar_url`: URLs para las acciones correspondientes

## Configuración

### Cambiar elementos por página

```php
// En el componente, puedes modificar la propiedad $perPage
public $perPage = 15; // Por defecto es 10
```

### Personalizar búsqueda

La búsqueda funciona en los siguientes campos:
- `codigo`
- `cliente`
- `rnc_cedula`
- `plan`
- `referidor`

## Ejemplo Completo

Puedes ver un ejemplo completo en:
`resources/views/examples/search-estimate-table-example.blade.php`

## Personalización

### Modificar estilos

El componente usa clases de Tailwind CSS con el tema de Filament. Puedes personalizar los estilos editando la vista:
`resources/views/livewire/search-estimate-table.blade.php`

### Agregar columnas

Para agregar nuevas columnas:

1. Modifica la vista para agregar el `<th>` en el header
2. Agrega el `<td>` correspondiente en el body
3. Si quieres que sea buscable, modifica el método `getFilteredCotizacionesProperty()` en el componente PHP

### Cambiar acciones

Para personalizar los botones de acción, modifica la sección de "Opciones" en la vista Blade.

## Eventos Livewire

El componente no emite eventos personalizados, pero puedes extenderlo para agregar funcionalidad adicional.

## Troubleshooting

### La tabla no se muestra
- Verifica que el array `$cotizaciones` tenga el formato correcto
- Asegúrate de que Livewire esté correctamente configurado

### La paginación no funciona
- Verifica que el trait `WithPagination` esté incluido en el componente
- Asegúrate de que `pagination_theme` esté configurado como 'tailwind' en `config/livewire.php`

### Los estilos no se ven correctamente
- Verifica que Filament esté correctamente instalado y configurado
- Asegúrate de que los assets de Tailwind CSS estén compilados

## Soporte

Este componente está diseñado para trabajar con:
- Laravel 10+
- Livewire 3+
- Filament 3+
- Tailwind CSS
