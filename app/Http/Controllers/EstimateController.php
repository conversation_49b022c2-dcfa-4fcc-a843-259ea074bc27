<?php

namespace App\Http\Controllers;

use App\Helpers\Zoho;
use Illuminate\Http\Request;

class EstimateController extends Controller
{
    public function download(string $id)
    {
        $libreria = new Zoho();
        // obtener datos de la cotizacion
        $cotizacion = $libreria->getRecord("Quotes", $id);

        if ($cotizacion->getFieldValue('Quote_Stage') == "Cotizando") {
            return view('cotizaciones/cotizacion', [
                "cotizacion" => $cotizacion,
                "libreria" => $libreria
            ]);
        } else {
            // informacion sobre las coberturas, la aseguradora,las coberturas
            $plan = $libreria->getRecord("Products", $cotizacion->getFieldValue("Coberturas")
                ->getEntityId());
            return view('cotizaciones/emision', [
                "cotizacion" => $cotizacion,
                "plan" => $plan,
                "libreria" => $libreria
            ]);
        }
    }
}
