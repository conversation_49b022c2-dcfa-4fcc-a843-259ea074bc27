<?php

use Illuminate\Support\Facades\Route;
use Filament\Http\Middleware\Authenticate;

Route::get('/', function () {
    return redirect('/admin');
});

Route::middleware(Authenticate::class)
    ->prefix('filament/resources')
    ->name('filament.resources.')
    ->group(function () {
        Route::get('estimates/{id}/download', [\App\Http\Controllers\EstimateController::class, 'download'])->name('estimate.download');
    });
